import React from 'react';
import { Congregation, TabType } from '../types';
import { AlertTriangle } from 'lucide-react';
import './TabNavigation.css';

interface TabNavigationProps {
  congregations: Congregation[];
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  criticalCount: number;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  congregations,
  activeTab,
  onTabChange,
  criticalCount,
}) => {
  const tabs = [
    { id: 'all', label: 'All Congregations', count: 0 },
    { id: 'critical', label: 'Critical Members', count: criticalCount, isAlert: true },
    ...congregations.map(congregation => ({
      id: congregation.id,
      label: congregation.name,
      count: 0,
    })),
  ];

  return (
    <nav className="tab-navigation" role="tablist">
      <div className="tab-list">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`tabpanel-${tab.id}`}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''} ${tab.isAlert ? 'alert' : ''}`}
            onClick={() => onTabChange(tab.id as TabType)}
          >
            <span className="tab-label">{tab.label}</span>
            {tab.count > 0 && (
              <span className={`tab-badge ${tab.isAlert ? 'alert' : ''}`}>
                {tab.isAlert && <AlertTriangle size={12} />}
                {tab.count}
              </span>
            )}
          </button>
        ))}
      </div>
    </nav>
  );
};

export default TabNavigation;
