/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [["path", { d: "M6 9h6V5l7 7-7 7v-4H6V9z", key: "7fvt9c" }]];
const ArrowBigRight = createLucideIcon("arrow-big-right", __iconNode);

export { __iconNode, ArrowBigRight as default };
//# sourceMappingURL=arrow-big-right.js.map
