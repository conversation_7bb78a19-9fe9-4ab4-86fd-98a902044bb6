{"name": "react-router", "version": "7.6.2", "description": "Declarative routing for React", "keywords": ["react", "router", "route", "routing", "history", "link"], "repository": {"type": "git", "url": "https://github.com/remix-run/react-router", "directory": "packages/react-router"}, "license": "MIT", "author": "Remix Software <<EMAIL>>", "sideEffects": false, "types": "./dist/development/index.d.ts", "main": "./dist/development/index.js", "module": "./dist/development/index.mjs", "exports": {".": {"node": {"types": "./dist/development/index.d.ts", "module-sync": "./dist/development/index.mjs", "default": "./dist/development/index.js"}, "import": {"types": "./dist/development/index.d.mts", "default": "./dist/development/index.mjs"}, "default": {"types": "./dist/development/index.d.ts", "default": "./dist/development/index.js"}}, "./internal": {"node": {"types": "./dist/development/lib/types/internal.d.ts"}, "import": {"types": "./dist/development/lib/types/internal.d.mts"}, "default": {"types": "./dist/development/lib/types/index.d.ts"}}, "./dom": {"node": {"types": "./dist/development/dom-export.d.ts", "module-sync": "./dist/development/dom-export.mjs", "default": "./dist/development/dom-export.js"}, "import": {"types": "./dist/development/dom-export.d.mts", "default": "./dist/development/dom-export.mjs"}, "default": {"types": "./dist/development/dom-export.d.ts", "default": "./dist/development/dom-export.js"}}, "./package.json": "./package.json"}, "wireit": {"build": {"command": "rimraf dist && tsup", "files": ["lib/**", "*.ts", "tsconfig.json", "package.json"], "output": ["dist/**"]}}, "dependencies": {"cookie": "^1.0.1", "set-cookie-parser": "^2.6.0"}, "devDependencies": {"@types/set-cookie-parser": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "rimraf": "^6.0.1", "tsup": "^8.3.0", "typescript": "^5.1.6", "wireit": "0.14.9"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md"], "engines": {"node": ">=20.0.0"}, "scripts": {"build": "wireit", "typecheck": "tsc"}}