"use strict";
exports.enNZ = void 0;
var _index = require("./en-US/_lib/formatDistance.cjs");
var _index2 = require("./en-US/_lib/formatRelative.cjs");
var _index3 = require("./en-US/_lib/localize.cjs");
var _index4 = require("./en-US/_lib/match.cjs");

var _index5 = require("./en-NZ/_lib/formatLong.cjs");

/**
 * @category Locales
 * @summary English locale (New Zealand).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@muntact](https://github.com/muntact)
 */
const enNZ = (exports.enNZ = {
  code: "en-NZ",
  formatDistance: _index.formatDistance,
  formatLong: _index5.formatLong,
  formatRelative: _index2.formatRelative,
  localize: _index3.localize,
  match: _index4.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
