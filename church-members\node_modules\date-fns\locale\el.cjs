"use strict";
exports.el = void 0;
var _index = require("./el/_lib/formatDistance.cjs");
var _index2 = require("./el/_lib/formatLong.cjs");
var _index3 = require("./el/_lib/formatRelative.cjs");
var _index4 = require("./el/_lib/localize.cjs");
var _index5 = require("./el/_lib/match.cjs");

/**
 * @category Locales
 * @summary Greek locale.
 * @language Greek
 * @iso-639-2 ell
 * <AUTHOR> [@fanixk](https://github.com/fanixk)
 * <AUTHOR> [@teoulas](https://github.com/teoulas)
 */
const el = (exports.el = {
  code: "el",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
