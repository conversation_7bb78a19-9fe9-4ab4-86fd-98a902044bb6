/**
 * @license lucide-react v0.515.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "6", height: "14", x: "2", y: "5", rx: "2", key: "dy24zr" }],
  ["rect", { width: "6", height: "10", x: "12", y: "7", rx: "2", key: "1ht384" }],
  ["path", { d: "M22 2v20", key: "40qfg1" }]
];
const AlignHorizontalJustifyEnd = createLucideIcon("align-horizontal-justify-end", __iconNode);

export { __iconNode, AlignHorizontalJustifyEnd as default };
//# sourceMappingURL=align-horizontal-justify-end.js.map
