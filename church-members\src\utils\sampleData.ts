// Sample data for testing the church membership application

import { Member, AttendanceRecord } from '../types';
import { formatDateForStorage } from './dateUtils';

export const sampleMembers: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>[] = [
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phoneNumber: '(*************',
    buildingAddress: '123 Main St, Anytown, ST 12345',
    bornAgainStatus: true,
    congregationGroup: 'main',
  },
  {
    firstName: 'Mary',
    lastName: '<PERSON>',
    phoneNumber: '(*************',
    buildingAddress: '456 Oak Ave, Anytown, ST 12345',
    bornAgainStatus: true,
    congregationGroup: 'main',
  },
  {
    firstName: 'David',
    lastName: 'Williams',
    phoneNumber: '(*************',
    buildingAddress: '789 Pine Rd, Anytown, ST 12345',
    bornAgainStatus: false,
    congregationGroup: 'youth',
  },
  {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phoneNumber: '(*************',
    buildingAddress: '321 Elm St, Anytown, ST 12345',
    bornAgainStatus: true,
    congregationGroup: 'seniors',
  },
  {
    firstName: '<PERSON>',
    lastName: 'Davis',
    phoneNumber: '(*************',
    buildingAddress: '654 Maple Dr, Anytown, ST 12345',
    bornAgainStatus: true,
    congregationGroup: 'youth',
  },
];

// Generate sample attendance records for the last few Sundays
export const generateSampleAttendance = (members: Member[]): Omit<AttendanceRecord, 'id' | 'createdDate'>[] => {
  const records: Omit<AttendanceRecord, 'id' | 'createdDate'>[] = [];
  
  // Get last 4 Sundays
  const sundays: Date[] = [];
  const today = new Date();
  let currentDate = new Date(today);
  
  // Find the most recent Sunday
  while (currentDate.getDay() !== 0) {
    currentDate.setDate(currentDate.getDate() - 1);
  }
  
  // Get last 4 Sundays
  for (let i = 0; i < 4; i++) {
    sundays.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() - 7);
  }
  
  // Generate attendance records
  members.forEach((member, memberIndex) => {
    sundays.forEach((sunday, sundayIndex) => {
      // Create some pattern of attendance/absence
      let status: 'present' | 'absent';
      
      if (memberIndex === 0) {
        // John Smith - perfect attendance
        status = 'present';
      } else if (memberIndex === 1) {
        // Mary Johnson - missed last 2 Sundays (critical)
        status = sundayIndex < 2 ? 'absent' : 'present';
      } else if (memberIndex === 2) {
        // David Williams - missed last 3 Sundays (critical)
        status = sundayIndex < 3 ? 'absent' : 'present';
      } else if (memberIndex === 3) {
        // Sarah Brown - good attendance, missed one
        status = sundayIndex === 1 ? 'absent' : 'present';
      } else {
        // Michael Davis - sporadic attendance
        status = sundayIndex % 2 === 0 ? 'present' : 'absent';
      }
      
      records.push({
        memberId: member.id,
        date: formatDateForStorage(sunday),
        status,
      });
    });
  });
  
  return records;
};

export const initializeSampleData = async () => {
  // Check if data already exists
  const existingMembers = localStorage.getItem('church_members');
  if (existingMembers && JSON.parse(existingMembers).length > 0) {
    return; // Data already exists
  }

  // Add sample members
  const { db } = await import('../services/database');
  const addedMembers: Member[] = [];

  sampleMembers.forEach(memberData => {
    const member = db.saveMember(memberData);
    addedMembers.push(member);
  });

  // Add sample attendance
  const attendanceRecords = generateSampleAttendance(addedMembers);
  attendanceRecords.forEach(recordData => {
    db.saveAttendanceRecord(recordData);
  });

  console.log('Sample data initialized:', {
    members: addedMembers.length,
    attendanceRecords: attendanceRecords.length,
  });
};
