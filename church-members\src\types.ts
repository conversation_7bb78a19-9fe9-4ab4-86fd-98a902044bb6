// Type definitions for the church membership application

export interface Member {
  id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  buildingAddress: string;
  bornAgainStatus: boolean;
  congregationGroup: string;
  createdDate: string;
  lastUpdated: string;
}

export interface AttendanceRecord {
  id: string;
  memberId: string;
  date: string; // YYYY-MM-DD format
  status: 'present' | 'absent';
  createdDate: string;
}

export interface Congregation {
  id: string;
  name: string;
  description?: string;
  createdDate: string;
}

export interface MemberWithAttendance extends Member {
  attendanceRecords: AttendanceRecord[];
  consecutiveAbsences: number;
  isCritical: boolean;
}

export interface AttendanceStats {
  totalMembers: number;
  presentCount: number;
  absentCount: number;
  attendancePercentage: number;
}

export interface DashboardStats {
  totalActiveMembers: number;
  currentMonthAttendancePercentage: number;
  criticalMembersCount: number;
  congregationStats: Record<string, AttendanceStats>;
}

export type TabType = 'all' | 'critical' | string; // string for congregation names
