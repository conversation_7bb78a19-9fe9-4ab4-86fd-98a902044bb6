import React from 'react';
import type { Congregation } from '../types';
import { AlertTriangle, Users, Heart, BarChart3 } from 'lucide-react';
import './ModernTabNavigation.css';

interface ModernTabNavigationProps {
  congregations: Congregation[];
  activeTab: string;
  onTabChange: (tab: string) => void;
  criticalCount: number;
  membersCount: number;
}

const ModernTabNavigation: React.FC<ModernTabNavigationProps> = ({
  congregations,
  activeTab,
  onTabChange,
  criticalCount,
  membersCount,
}) => {
  const getTabIcon = (tabId: string) => {
    switch (tabId) {
      case 'overview':
        return <BarChart3 size={20} />;
      case 'critical':
        return <AlertTriangle size={20} />;
      case 'new-believers':
        return <Heart size={20} />;
      default:
        return <Users size={20} />;
    }
  };

  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'overview':
        return membersCount;
      case 'critical':
        return criticalCount;
      case 'new-believers':
        // Count members created in last 30 days
        return 0; // This would be calculated in the parent component
      default:
        return 0;
    }
  };

  const getTabColor = (tabId: string) => {
    switch (tabId) {
      case 'critical':
        return 'danger';
      case 'new-believers':
        return 'success';
      default:
        return 'primary';
    }
  };

  return (
    <nav className="modern-tab-navigation">
      <div className="tab-container">
        {congregations.map((congregation) => {
          const isActive = activeTab === congregation.id;
          const count = getTabCount(congregation.id);
          const color = getTabColor(congregation.id);
          
          return (
            <button
              key={congregation.id}
              className={`modern-tab ${isActive ? 'active' : ''} ${color}`}
              onClick={() => onTabChange(congregation.id)}
            >
              <div className="tab-icon">
                {getTabIcon(congregation.id)}
              </div>
              <div className="tab-content">
                <span className="tab-name">{congregation.name}</span>
                {congregation.description && (
                  <span className="tab-description">{congregation.description}</span>
                )}
              </div>
              {count > 0 && (
                <div className={`tab-badge ${color}`}>
                  {count}
                </div>
              )}
              {isActive && <div className="active-indicator" />}
            </button>
          );
        })}
      </div>
    </nav>
  );
};

export default ModernTabNavigation;
