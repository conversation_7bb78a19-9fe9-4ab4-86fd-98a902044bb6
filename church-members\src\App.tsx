import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { db } from './services/database';
import { Member, AttendanceRecord, Congregation, MemberWithAttendance, TabType } from './types';
import { enhanceMembersWithAttendance, getCriticalMembers } from './utils/attendanceUtils';
import { getCurrentMonthSundays } from './utils/dateUtils';
import TabNavigation from './components/TabNavigation';
import MemberTable from './components/MemberTable';
import Dashboard from './components/Dashboard';
import MemberForm from './components/MemberForm';
import './App.css';

function App() {
  const [members, setMembers] = useState<Member[]>([]);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [congregations, setCongregations] = useState<Congregation[]>([]);
  const [activeTab, setActiveTab] = useState<TabType>('all');
  const [showMemberForm, setShowMemberForm] = useState(false);
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [loading, setLoading] = useState(true);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [membersData, attendanceData, congregationsData] = await Promise.all([
        Promise.resolve(db.getMembers()),
        Promise.resolve(db.getAttendanceRecords()),
        Promise.resolve(db.getCongregations()),
      ]);

      setMembers(membersData);
      setAttendanceRecords(attendanceData);
      setCongregations(congregationsData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get enhanced members with attendance data
  const membersWithAttendance: MemberWithAttendance[] = enhanceMembersWithAttendance(
    members,
    attendanceRecords
  );

  // Filter members based on active tab
  const getFilteredMembers = (): MemberWithAttendance[] => {
    if (activeTab === 'all') {
      return membersWithAttendance;
    } else if (activeTab === 'critical') {
      return getCriticalMembers(membersWithAttendance);
    } else {
      // Filter by congregation
      return membersWithAttendance.filter(member => member.congregationGroup === activeTab);
    }
  };

  const filteredMembers = getFilteredMembers();

  // Get current month Sundays for attendance columns
  const currentMonthSundays = getCurrentMonthSundays();

  // Handle member save
  const handleSaveMember = async (memberData: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>) => {
    try {
      if (editingMember) {
        // Update existing member
        const updatedMember = db.updateMember(editingMember.id, memberData);
        if (updatedMember) {
          setMembers(prev => prev.map(m => m.id === editingMember.id ? updatedMember : m));
        }
      } else {
        // Create new member
        const newMember = db.saveMember(memberData);
        setMembers(prev => [...prev, newMember]);
      }

      setShowMemberForm(false);
      setEditingMember(null);
    } catch (error) {
      console.error('Error saving member:', error);
    }
  };

  // Handle member delete
  const handleDeleteMember = async (memberId: string) => {
    if (window.confirm('Are you sure you want to delete this member?')) {
      try {
        const success = db.deleteMember(memberId);
        if (success) {
          setMembers(prev => prev.filter(m => m.id !== memberId));
          setAttendanceRecords(prev => prev.filter(r => r.memberId !== memberId));
        }
      } catch (error) {
        console.error('Error deleting member:', error);
      }
    }
  };

  // Handle attendance toggle
  const handleAttendanceToggle = async (memberId: string, date: string, currentStatus: 'present' | 'absent' | null) => {
    try {
      const newStatus: 'present' | 'absent' = currentStatus === 'present' ? 'absent' : 'present';

      const attendanceRecord = db.saveAttendanceRecord({
        memberId,
        date,
        status: newStatus,
      });

      setAttendanceRecords(prev => {
        const filtered = prev.filter(r => !(r.memberId === memberId && r.date === date));
        return [...filtered, attendanceRecord];
      });
    } catch (error) {
      console.error('Error updating attendance:', error);
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">Loading...</div>
      </div>
    );
  }

  return (
    <Router>
      <div className="app">
        <header className="app-header">
          <div className="container">
            <h1>Church Membership Management</h1>
          </div>
        </header>

        <main className="app-main">
          <div className="container">
            <Routes>
              <Route path="/dashboard" element={
                <Dashboard
                  members={membersWithAttendance}
                  attendanceRecords={attendanceRecords}
                  congregations={congregations}
                />
              } />
              <Route path="/" element={
                <>
                  <TabNavigation
                    congregations={congregations}
                    activeTab={activeTab}
                    onTabChange={setActiveTab}
                    criticalCount={getCriticalMembers(membersWithAttendance).length}
                  />

                  <div className="member-actions">
                    <button
                      className="btn-primary"
                      onClick={() => setShowMemberForm(true)}
                    >
                      Add New Member
                    </button>
                    <span className="member-count">
                      {filteredMembers.length} member{filteredMembers.length !== 1 ? 's' : ''}
                    </span>
                  </div>

                  <MemberTable
                    members={filteredMembers}
                    attendanceRecords={attendanceRecords}
                    sundays={currentMonthSundays}
                    onEditMember={(member) => {
                      setEditingMember(member);
                      setShowMemberForm(true);
                    }}
                    onDeleteMember={handleDeleteMember}
                    onAttendanceToggle={handleAttendanceToggle}
                  />
                </>
              } />
            </Routes>
          </div>
        </main>

        {showMemberForm && (
          <MemberForm
            member={editingMember}
            congregations={congregations}
            onSave={handleSaveMember}
            onCancel={() => {
              setShowMemberForm(false);
              setEditingMember(null);
            }}
          />
        )}
      </div>
    </Router>
  );
}

export default App;
