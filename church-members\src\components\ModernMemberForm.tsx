import React, { useState, useEffect } from 'react';
import type { Member, Congregation } from '../types';
import { X, User, Phone, MapPin, Heart, Users } from 'lucide-react';
import './ModernMemberForm.css';

interface ModernMemberFormProps {
  member?: Member | null;
  congregations: Congregation[];
  onSave: (member: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>) => void;
  onCancel: () => void;
}

const ModernMemberForm: React.FC<ModernMemberFormProps> = ({
  member,
  congregations,
  onSave,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    buildingAddress: '',
    bornAgainStatus: false,
    congregationGroup: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Populate form when editing
  useEffect(() => {
    if (member) {
      setFormData({
        firstName: member.firstName,
        lastName: member.lastName,
        phoneNumber: member.phoneNumber,
        buildingAddress: member.buildingAddress,
        bornAgainStatus: member.bornAgainStatus,
        congregationGroup: member.congregationGroup,
      });
    } else {
      // Set default congregation to first available (excluding system tabs)
      const userTabs = congregations.filter(c => 
        !['overview', 'critical', 'new-believers'].includes(c.id)
      );
      if (userTabs.length > 0) {
        setFormData(prev => ({ ...prev, congregationGroup: userTabs[0].id }));
      }
    }
  }, [member, congregations]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    if (!formData.buildingAddress.trim()) {
      newErrors.buildingAddress = 'Address is required';
    }

    if (!formData.congregationGroup) {
      newErrors.congregationGroup = 'Please select a group';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving member:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatPhoneNumber = (value: string) => {
    const cleaned = value.replace(/\D/g, '');
    
    if (cleaned.length >= 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    } else if (cleaned.length >= 3) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    } else {
      return cleaned;
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    handleInputChange('phoneNumber', formatted);
  };

  // Filter out system tabs for congregation selection
  const availableCongregations = congregations.filter(c => 
    !['overview', 'critical', 'new-believers'].includes(c.id)
  );

  return (
    <div className="modern-modal-overlay" onClick={onCancel}>
      <div className="modern-modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modern-modal-header">
          <div className="modal-title-section">
            <div className="modal-icon">
              <User size={24} />
            </div>
            <h2>{member ? 'Edit Member' : 'Add New Member'}</h2>
          </div>
          <button
            type="button"
            className="modern-close-button"
            onClick={onCancel}
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="modern-member-form">
          <div className="form-section">
            <h3 className="section-title">
              <User size={20} />
              Personal Information
            </h3>
            
            <div className="form-row">
              <div className="modern-form-group">
                <label htmlFor="firstName">First Name *</label>
                <input
                  type="text"
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={errors.firstName ? 'error' : ''}
                  placeholder="Enter first name"
                />
                {errors.firstName && <span className="modern-error-message">{errors.firstName}</span>}
              </div>

              <div className="modern-form-group">
                <label htmlFor="lastName">Last Name *</label>
                <input
                  type="text"
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={errors.lastName ? 'error' : ''}
                  placeholder="Enter last name"
                />
                {errors.lastName && <span className="modern-error-message">{errors.lastName}</span>}
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3 className="section-title">
              <Phone size={20} />
              Contact Information
            </h3>
            
            <div className="modern-form-group">
              <label htmlFor="phoneNumber">Phone Number *</label>
              <input
                type="tel"
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={handlePhoneChange}
                className={errors.phoneNumber ? 'error' : ''}
                placeholder="(*************"
              />
              {errors.phoneNumber && <span className="modern-error-message">{errors.phoneNumber}</span>}
            </div>

            <div className="modern-form-group">
              <label htmlFor="buildingAddress">Address *</label>
              <textarea
                id="buildingAddress"
                value={formData.buildingAddress}
                onChange={(e) => handleInputChange('buildingAddress', e.target.value)}
                className={errors.buildingAddress ? 'error' : ''}
                placeholder="Enter full address"
                rows={3}
              />
              {errors.buildingAddress && <span className="modern-error-message">{errors.buildingAddress}</span>}
            </div>
          </div>

          <div className="form-section">
            <h3 className="section-title">
              <Users size={20} />
              Church Information
            </h3>
            
            <div className="form-row">
              <div className="modern-form-group">
                <label htmlFor="congregationGroup">Group *</label>
                <select
                  id="congregationGroup"
                  value={formData.congregationGroup}
                  onChange={(e) => handleInputChange('congregationGroup', e.target.value)}
                  className={errors.congregationGroup ? 'error' : ''}
                >
                  <option value="">Select a group</option>
                  {availableCongregations.map((congregation) => (
                    <option key={congregation.id} value={congregation.id}>
                      {congregation.name}
                    </option>
                  ))}
                </select>
                {errors.congregationGroup && <span className="modern-error-message">{errors.congregationGroup}</span>}
              </div>

              <div className="modern-form-group">
                <label className="modern-checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.bornAgainStatus}
                    onChange={(e) => handleInputChange('bornAgainStatus', e.target.checked)}
                    className="modern-checkbox"
                  />
                  <div className="checkbox-custom">
                    <Heart size={16} />
                  </div>
                  <span className="checkbox-text">Born Again</span>
                </label>
              </div>
            </div>
          </div>

          <div className="modern-form-actions">
            <button
              type="button"
              className="btn-secondary cancel-btn"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary submit-btn"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="spinner" />
                  Saving...
                </>
              ) : (
                member ? 'Update Member' : 'Add Member'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModernMemberForm;
