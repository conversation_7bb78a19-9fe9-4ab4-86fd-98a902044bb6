/* Modern Church Membership Management - Beautiful Design */

:root {
  /* Modern Color Palette */
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --primary-light: #e0e7ff;
  --secondary-color: #6366f1;
  --success-color: #10b981;
  --success-light: #d1fae5;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --surface-hover: #f1f5f9;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #94a3b8;
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Touch targets */
  --touch-target: 48px;

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Modern Shadows */
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Animations */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: var(--background-color);
  color: var(--text-primary);
  font-size: 16px;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Mobile-first responsive typography */
h1 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 var(--spacing-lg) 0;
}

h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin: 0 0 var(--spacing-md) 0;
}

h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 var(--spacing-sm) 0;
}

p {
  margin: 0 0 var(--spacing-md) 0;
  line-height: 1.6;
}

/* Modern Button Styles */
button {
  min-height: var(--touch-target);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

button:active {
  transform: translateY(0);
  transition: all 0.1s ease-out;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
  box-shadow: var(--shadow-xl);
}

.btn-secondary {
  background: var(--surface-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--surface-hover);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-success {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-success:hover {
  background: linear-gradient(135deg, #68d391 0%, #4fd1c7 100%);
}

.btn-warning {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background: var(--gradient-danger);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-sm {
  min-height: 36px;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
}

.btn-lg {
  min-height: 52px;
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1.125rem;
}

/* Modern Input Styles */
input, textarea, select {
  min-height: var(--touch-target);
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: inherit;
  background: var(--surface-color);
  transition: all var(--transition-fast);
  width: 100%;
  box-shadow: var(--shadow-sm);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light), var(--shadow-md);
  background: white;
  transform: translateY(-1px);
}

input:hover, textarea:hover, select:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

/* Container and layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Tablet styles */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-lg);
  }

  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.875rem;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-xl);
  }

  h1 {
    font-size: 3rem;
  }
}
