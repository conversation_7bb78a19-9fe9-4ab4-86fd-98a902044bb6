"use strict";
exports.de = void 0;
var _index = require("./de/_lib/formatDistance.cjs");
var _index2 = require("./de/_lib/formatLong.cjs");
var _index3 = require("./de/_lib/formatRelative.cjs");
var _index4 = require("./de/_lib/localize.cjs");
var _index5 = require("./de/_lib/match.cjs");

/**
 * @category Locales
 * @summary German locale.
 * @language German
 * @iso-639-2 deu
 * <AUTHOR> [@DeMuu](https://github.com/DeMuu)
 * <AUTHOR> [@asia-t](https://github.com/asia-t)
 * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)
 * <AUTHOR> [@pex](https://github.com/pex)
 * <AUTHOR> [@Philipp91](https://github.com/Philipp91)
 */
const de = (exports.de = {
  code: "de",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
