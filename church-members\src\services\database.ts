// Database service using localStorage for data persistence
// This can be easily migrated to SQLite later

import { Member, AttendanceRecord, Congregation } from '../types';

const STORAGE_KEYS = {
  MEMBERS: 'church_members',
  ATTENDANCE: 'church_attendance',
  CONGREGATIONS: 'church_congregations',
} as const;

// Initialize default congregations
const DEFAULT_CONGREGATIONS: Congregation[] = [
  {
    id: 'main',
    name: 'Main Congregation',
    description: 'Primary congregation group',
    createdDate: new Date().toISOString(),
  },
  {
    id: 'youth',
    name: 'Youth Group',
    description: 'Young adults and teenagers',
    createdDate: new Date().toISOString(),
  },
  {
    id: 'seniors',
    name: 'Senior Saints',
    description: 'Senior members group',
    createdDate: new Date().toISOString(),
  },
];

class DatabaseService {
  constructor() {
    this.initializeDatabase();
  }

  private initializeDatabase(): void {
    // Initialize congregations if they don't exist
    if (!localStorage.getItem(STORAGE_KEYS.CONGREGATIONS)) {
      this.saveCongregations(DEFAULT_CONGREGATIONS);
    }

    // Initialize empty arrays if they don't exist
    if (!localStorage.getItem(STORAGE_KEYS.MEMBERS)) {
      localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify([]));
    }
    if (!localStorage.getItem(STORAGE_KEYS.ATTENDANCE)) {
      localStorage.setItem(STORAGE_KEYS.ATTENDANCE, JSON.stringify([]));
    }
  }

  // Member CRUD operations
  getMembers(): Member[] {
    const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);
    return data ? JSON.parse(data) : [];
  }

  getMemberById(id: string): Member | null {
    const members = this.getMembers();
    return members.find(member => member.id === id) || null;
  }

  saveMember(member: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>): Member {
    const members = this.getMembers();
    const now = new Date().toISOString();
    
    const newMember: Member = {
      ...member,
      id: this.generateId(),
      createdDate: now,
      lastUpdated: now,
    };

    members.push(newMember);
    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));
    return newMember;
  }

  updateMember(id: string, updates: Partial<Omit<Member, 'id' | 'createdDate'>>): Member | null {
    const members = this.getMembers();
    const memberIndex = members.findIndex(member => member.id === id);
    
    if (memberIndex === -1) return null;

    const updatedMember = {
      ...members[memberIndex],
      ...updates,
      lastUpdated: new Date().toISOString(),
    };

    members[memberIndex] = updatedMember;
    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));
    return updatedMember;
  }

  deleteMember(id: string): boolean {
    const members = this.getMembers();
    const filteredMembers = members.filter(member => member.id !== id);
    
    if (filteredMembers.length === members.length) return false;

    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(filteredMembers));
    
    // Also delete related attendance records
    const attendance = this.getAttendanceRecords();
    const filteredAttendance = attendance.filter(record => record.memberId !== id);
    localStorage.setItem(STORAGE_KEYS.ATTENDANCE, JSON.stringify(filteredAttendance));
    
    return true;
  }

  // Attendance CRUD operations
  getAttendanceRecords(): AttendanceRecord[] {
    const data = localStorage.getItem(STORAGE_KEYS.ATTENDANCE);
    return data ? JSON.parse(data) : [];
  }

  getAttendanceByMember(memberId: string): AttendanceRecord[] {
    const records = this.getAttendanceRecords();
    return records.filter(record => record.memberId === memberId);
  }

  getAttendanceByDate(date: string): AttendanceRecord[] {
    const records = this.getAttendanceRecords();
    return records.filter(record => record.date === date);
  }

  saveAttendanceRecord(record: Omit<AttendanceRecord, 'id' | 'createdDate'>): AttendanceRecord {
    const records = this.getAttendanceRecords();
    
    // Check if record already exists for this member and date
    const existingIndex = records.findIndex(
      r => r.memberId === record.memberId && r.date === record.date
    );

    const newRecord: AttendanceRecord = {
      ...record,
      id: existingIndex >= 0 ? records[existingIndex].id : this.generateId(),
      createdDate: existingIndex >= 0 ? records[existingIndex].createdDate : new Date().toISOString(),
    };

    if (existingIndex >= 0) {
      records[existingIndex] = newRecord;
    } else {
      records.push(newRecord);
    }

    localStorage.setItem(STORAGE_KEYS.ATTENDANCE, JSON.stringify(records));
    return newRecord;
  }

  // Congregation operations
  getCongregations(): Congregation[] {
    const data = localStorage.getItem(STORAGE_KEYS.CONGREGATIONS);
    return data ? JSON.parse(data) : DEFAULT_CONGREGATIONS;
  }

  private saveCongregations(congregations: Congregation[]): void {
    localStorage.setItem(STORAGE_KEYS.CONGREGATIONS, JSON.stringify(congregations));
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

export const db = new DatabaseService();
