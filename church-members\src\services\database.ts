// Database service using localStorage for data persistence
// This can be easily migrated to SQLite later

import type { Member, AttendanceRecord, Congregation } from '../types';

const STORAGE_KEYS = {
  MEMBERS: 'church_members',
  ATTENDANCE: 'church_attendance',
  CONGREGATIONS: 'church_congregations',
} as const;

// Default congregations will be initialized by the app
const DEFAULT_CONGREGATIONS: Congregation[] = [];

class DatabaseService {
  constructor() {
    this.initializeDatabase();
  }

  private initializeDatabase(): void {
    // Initialize empty arrays if they don't exist
    if (!localStorage.getItem(STORAGE_KEYS.MEMBERS)) {
      localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify([]));
    }
    if (!localStorage.getItem(STORAGE_KEYS.ATTENDANCE)) {
      localStorage.setItem(STORAGE_KEYS.ATTENDANCE, JSON.stringify([]));
    }
    if (!localStorage.getItem(STORAGE_KEYS.CONGREGATIONS)) {
      localStorage.setItem(STORAGE_KEYS.CONGREGATIONS, JSON.stringify([]));
    }
  }

  // Member CRUD operations
  getMembers(): Member[] {
    const data = localStorage.getItem(STORAGE_KEYS.MEMBERS);
    return data ? JSON.parse(data) : [];
  }

  getMemberById(id: string): Member | null {
    const members = this.getMembers();
    return members.find(member => member.id === id) || null;
  }

  saveMember(member: Omit<Member, 'id' | 'createdDate' | 'lastUpdated'>): Member {
    const members = this.getMembers();
    const now = new Date().toISOString();
    
    const newMember: Member = {
      ...member,
      id: this.generateId(),
      createdDate: now,
      lastUpdated: now,
    };

    members.push(newMember);
    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));
    return newMember;
  }

  updateMember(id: string, updates: Partial<Omit<Member, 'id' | 'createdDate'>>): Member | null {
    const members = this.getMembers();
    const memberIndex = members.findIndex(member => member.id === id);
    
    if (memberIndex === -1) return null;

    const updatedMember = {
      ...members[memberIndex],
      ...updates,
      lastUpdated: new Date().toISOString(),
    };

    members[memberIndex] = updatedMember;
    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(members));
    return updatedMember;
  }

  deleteMember(id: string): boolean {
    const members = this.getMembers();
    const filteredMembers = members.filter(member => member.id !== id);
    
    if (filteredMembers.length === members.length) return false;

    localStorage.setItem(STORAGE_KEYS.MEMBERS, JSON.stringify(filteredMembers));
    
    // Also delete related attendance records
    const attendance = this.getAttendanceRecords();
    const filteredAttendance = attendance.filter(record => record.memberId !== id);
    localStorage.setItem(STORAGE_KEYS.ATTENDANCE, JSON.stringify(filteredAttendance));
    
    return true;
  }

  // Attendance CRUD operations
  getAttendanceRecords(): AttendanceRecord[] {
    const data = localStorage.getItem(STORAGE_KEYS.ATTENDANCE);
    return data ? JSON.parse(data) : [];
  }

  getAttendanceByMember(memberId: string): AttendanceRecord[] {
    const records = this.getAttendanceRecords();
    return records.filter(record => record.memberId === memberId);
  }

  getAttendanceByDate(date: string): AttendanceRecord[] {
    const records = this.getAttendanceRecords();
    return records.filter(record => record.date === date);
  }

  saveAttendanceRecord(record: Omit<AttendanceRecord, 'id' | 'createdDate'>): AttendanceRecord {
    const records = this.getAttendanceRecords();
    
    // Check if record already exists for this member and date
    const existingIndex = records.findIndex(
      r => r.memberId === record.memberId && r.date === record.date
    );

    const newRecord: AttendanceRecord = {
      ...record,
      id: existingIndex >= 0 ? records[existingIndex].id : this.generateId(),
      createdDate: existingIndex >= 0 ? records[existingIndex].createdDate : new Date().toISOString(),
    };

    if (existingIndex >= 0) {
      records[existingIndex] = newRecord;
    } else {
      records.push(newRecord);
    }

    localStorage.setItem(STORAGE_KEYS.ATTENDANCE, JSON.stringify(records));
    return newRecord;
  }

  // Congregation operations
  getCongregations(): Congregation[] {
    const data = localStorage.getItem(STORAGE_KEYS.CONGREGATIONS);
    return data ? JSON.parse(data) : [];
  }

  saveCongregation(congregation: Omit<Congregation, 'id' | 'createdDate'>): Congregation {
    const congregations = this.getCongregations();
    const now = new Date().toISOString();

    const newCongregation: Congregation = {
      ...congregation,
      id: this.generateId(),
      createdDate: now,
    };

    congregations.push(newCongregation);
    this.saveCongregations(congregations);
    return newCongregation;
  }

  updateCongregation(id: string, updates: Partial<Omit<Congregation, 'id' | 'createdDate'>>): Congregation | null {
    const congregations = this.getCongregations();
    const congregationIndex = congregations.findIndex(congregation => congregation.id === id);

    if (congregationIndex === -1) return null;

    const updatedCongregation = {
      ...congregations[congregationIndex],
      ...updates,
    };

    congregations[congregationIndex] = updatedCongregation;
    this.saveCongregations(congregations);
    return updatedCongregation;
  }

  deleteCongregation(id: string): boolean {
    const congregations = this.getCongregations();
    const filteredCongregations = congregations.filter(congregation => congregation.id !== id);

    if (filteredCongregations.length === congregations.length) return false;

    this.saveCongregations(filteredCongregations);
    return true;
  }

  private saveCongregations(congregations: Congregation[]): void {
    localStorage.setItem(STORAGE_KEYS.CONGREGATIONS, JSON.stringify(congregations));
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

export const db = new DatabaseService();
