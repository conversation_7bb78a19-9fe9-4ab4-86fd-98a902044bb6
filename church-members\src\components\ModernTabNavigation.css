/* Modern Tab Navigation Styles */

.modern-tab-navigation {
  margin-bottom: var(--spacing-lg);
}

.tab-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
}

.modern-tab {
  background: var(--surface-color);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
  overflow: hidden;
  min-height: 80px;
  box-shadow: var(--shadow-sm);
}

.modern-tab:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.modern-tab.active {
  background: var(--primary-light);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.modern-tab.active.danger {
  background: var(--danger-light);
  border-color: var(--danger-color);
}

.modern-tab.active.success {
  background: var(--success-light);
  border-color: var(--success-color);
}

.tab-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-fast);
}

.modern-tab.danger .tab-icon {
  background: var(--gradient-danger);
}

.modern-tab.success .tab-icon {
  background: var(--gradient-success);
}

.modern-tab:hover .tab-icon {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  text-align: left;
}

.tab-name {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
}

.tab-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.tab-badge {
  flex-shrink: 0;
  min-width: 32px;
  height: 32px;
  border-radius: var(--radius-lg);
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 700;
  box-shadow: var(--shadow-md);
  animation: pulse 2s infinite;
}

.tab-badge.danger {
  background: var(--danger-color);
  animation: pulse 1.5s infinite;
}

.tab-badge.success {
  background: var(--success-color);
}

.active-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.modern-tab.active.danger .active-indicator {
  background: var(--gradient-danger);
}

.modern-tab.active.success .active-indicator {
  background: var(--gradient-success);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .tab-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .modern-tab {
    padding: var(--spacing-md);
    min-height: 70px;
  }
  
  .tab-icon {
    width: 40px;
    height: 40px;
  }
  
  .tab-name {
    font-size: 1rem;
  }
  
  .tab-description {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .modern-tab {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  
  .tab-icon {
    width: 36px;
    height: 36px;
  }
  
  .tab-badge {
    min-width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Animation keyframes */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Smooth entrance animation */
.modern-tab {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
